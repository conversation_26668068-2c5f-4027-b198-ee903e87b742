<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ffsafe.scantaskapi.service.mapper.FfsafeHostscanTaskResultMapper">

    <resultMap type="FfsafeHostscanTaskResult" id="FfsafeHostscanTaskresultResult">
        <result property="id"    column="id"    />
        <result property="taskId"    column="task_id"    />
        <result property="ip"    column="ip"    />
        <result property="systemName"    column="system_name"    />
        <result property="systemVersion"    column="system_version"    />
        <result property="highRiskNum"    column="high_risk_num"    />
        <result property="middleRiskNum"    column="middle_risk_num"    />
        <result property="lowRiskNum"    column="low_risk_num"    />
        <result property="pocRiskNum"    column="poc_risk_num"    />
        <result property="pwNum"    column="pw_num"    />
        <result property="portNum"    column="port_num"    />
        <result property="summaryId"    column="summary_id"    />
    </resultMap>

    <sql id="selectFfsafeHostscanTaskresultVo">
        select id, task_id, ip, system_name, system_version, high_risk_num, middle_risk_num, low_risk_num, poc_risk_num, pw_num, port_num, summary_id from ffsafe_hostscan_taskresult
    </sql>

    <select id="selectFfsafeHostscanTaskresultList" parameterType="FfsafeHostscanTaskResult" resultMap="FfsafeHostscanTaskresultResult">
        <include refid="selectFfsafeHostscanTaskresultVo"/>
        <where>
            <if test="taskId != null "> and task_id = #{taskId}</if>
            <if test="ip != null  and ip != ''"> and ip = #{ip}</if>
            <if test="systemName != null  and systemName != ''"> and system_name like concat('%', #{systemName}, '%')</if>
            <if test="systemVersion != null  and systemVersion != ''"> and system_version = #{systemVersion}</if>
            <if test="highRiskNum != null "> and high_risk_num = #{highRiskNum}</if>
            <if test="middleRiskNum != null "> and middle_risk_num = #{middleRiskNum}</if>
            <if test="lowRiskNum != null "> and low_risk_num = #{lowRiskNum}</if>
            <if test="pocRiskNum != null "> and poc_risk_num = #{pocRiskNum}</if>
            <if test="pwNum != null "> and pw_num = #{pwNum}</if>
            <if test="portNum != null "> and port_num = #{portNum}</if>
            <if test="summaryId != null "> and summary_id = #{summaryId}</if>
        </where>
    </select>

    <select id="selectFfsafeHostscanTaskresultById" parameterType="Long" resultMap="FfsafeHostscanTaskresultResult">
        <include refid="selectFfsafeHostscanTaskresultVo"/>
        where id = #{id}
    </select>

    <select id="selectFfsafeHostscanTaskresultByIds" parameterType="Long" resultMap="FfsafeHostscanTaskresultResult">
        <include refid="selectFfsafeHostscanTaskresultVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertFfsafeHostscanTaskresult" parameterType="FfsafeHostscanTaskResult" useGeneratedKeys="true" keyProperty="id">
        insert into ffsafe_hostscan_taskresult
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">task_id,</if>
            <if test="ip != null">ip,</if>
            <if test="systemName != null">system_name,</if>
            <if test="systemVersion != null">system_version,</if>
            <if test="highRiskNum != null">high_risk_num,</if>
            <if test="middleRiskNum != null">middle_risk_num,</if>
            <if test="lowRiskNum != null">low_risk_num,</if>
            <if test="pocRiskNum != null">poc_risk_num,</if>
            <if test="pwNum != null">pw_num,</if>
            <if test="portNum != null">port_num,</if>
            <if test="summaryId != null">summary_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">#{taskId},</if>
            <if test="ip != null">#{ip},</if>
            <if test="systemName != null">#{systemName},</if>
            <if test="systemVersion != null">#{systemVersion},</if>
            <if test="highRiskNum != null">#{highRiskNum},</if>
            <if test="middleRiskNum != null">#{middleRiskNum},</if>
            <if test="lowRiskNum != null">#{lowRiskNum},</if>
            <if test="pocRiskNum != null">#{pocRiskNum},</if>
            <if test="pwNum != null">#{pwNum},</if>
            <if test="portNum != null">#{portNum},</if>
            <if test="summaryId != null">#{summaryId},</if>
        </trim>
    </insert>

    <update id="updateFfsafeHostscanTaskresult" parameterType="FfsafeHostscanTaskResult">
        update ffsafe_hostscan_taskresult
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="ip != null">ip = #{ip},</if>
            <if test="systemName != null">system_name = #{systemName},</if>
            <if test="systemVersion != null">system_version = #{systemVersion},</if>
            <if test="highRiskNum != null">high_risk_num = #{highRiskNum},</if>
            <if test="middleRiskNum != null">middle_risk_num = #{middleRiskNum},</if>
            <if test="lowRiskNum != null">low_risk_num = #{lowRiskNum},</if>
            <if test="pocRiskNum != null">poc_risk_num = #{pocRiskNum},</if>
            <if test="pwNum != null">pw_num = #{pwNum},</if>
            <if test="portNum != null">port_num = #{portNum},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFfsafeHostscanTaskresultById" parameterType="Long">
        delete from ffsafe_hostscan_taskresult where id = #{id}
    </delete>

    <delete id="deleteFfsafeHostscanTaskresultByIds" parameterType="String">
        delete from ffsafe_hostscan_taskresult where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>