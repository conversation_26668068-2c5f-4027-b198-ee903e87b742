<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ffsafe.scantaskapi.service.mapper.FfsafeHostscanVulnResultMapper">

    <resultMap type="FfsafeHostscanVulnResult" id="FfsafeHostscanVulnResultResult">
        <result property="id"    column="id"    />
        <result property="taskId"    column="task_id"    />
        <result property="hostIp"    column="host_ip"    />
        <result property="hostPort"    column="host_port"    />
        <result property="versionInfo"    column="version_info"    />
        <result property="vulnId"    column="vuln_id"    />
        <result property="vulnName"    column="vuln_name"    />
        <result property="riskLevel"    column="risk_level"    />
        <result property="cveNumber"    column="cve_number"    />
        <result property="cnnvdNumber"    column="cnnvd_number"    />
        <result property="vulnType"    column="vuln_type"    />
        <result property="publishDate"    column="publish_date"    />
        <result property="vulnInfo"    column="vuln_info"    />
        <result property="vulnSolve"    column="vuln_solve"    />
        <result property="evidence"    column="evidence"    />
        <result property="cpe"    column="cpe"    />
        <result property="summaryId"    column="summary_id"    />
    </resultMap>

    <sql id="selectFfsafeHostscanVulnresultVo">
        select id, task_id, host_ip, host_port, version_info, vuln_id, vuln_name, risk_level, cve_number, cnnvd_number, vuln_type, publish_date, vuln_info, vuln_solve, evidence, cpe, summary_id from ffsafe_hostscan_vulnresult
    </sql>

    <select id="selectFfsafeHostscanVulnResultList" parameterType="FfsafeHostscanVulnresult" resultMap="FfsafeHostscanVulnResultResult">
        <include refid="selectFfsafeHostscanVulnresultVo"/>
        <where>
            <if test="taskId != null "> and task_id = #{taskId}</if>
            <if test="hostIp != null  and hostIp != ''"> and host_ip = #{hostIp}</if>
            <if test="hostPort != null "> and host_port = #{hostPort}</if>
            <if test="versionInfo != null  and versionInfo != ''"> and version_info = #{versionInfo}</if>
            <if test="vulnId != null "> and vuln_id = #{vulnId}</if>
            <if test="vulnName != null  and vulnName != ''"> and vuln_name like concat('%', #{vulnName}, '%')</if>
            <if test="riskLevel != null "> and risk_level = #{riskLevel}</if>
            <if test="cveNumber != null  and cveNumber != ''"> and cve_number = #{cveNumber}</if>
            <if test="cnnvdNumber != null  and cnnvdNumber != ''"> and cnnvd_number = #{cnnvdNumber}</if>
            <if test="vulnType != null  and vulnType != ''"> and vuln_type = #{vulnType}</if>
            <if test="publishDate != null "> and publish_date = #{publishDate}</if>
            <if test="vulnInfo != null  and vulnInfo != ''"> and vuln_info = #{vulnInfo}</if>
            <if test="vulnSolve != null  and vulnSolve != ''"> and vuln_solve = #{vulnSolve}</if>
            <if test="evidence != null  and evidence != ''"> and evidence = #{evidence}</if>
            <if test="cpe != null  and cpe != ''"> and cpe = #{cpe}</if>
            <if test="summaryId != null "> and summary_id = #{summaryId}</if>
        </where>
    </select>

    <select id="selectFfsafeHostscanVulnResultById" parameterType="Long" resultMap="FfsafeHostscanVulnResultResult">
        <include refid="selectFfsafeHostscanVulnresultVo"/>
        where id = #{id}
    </select>

    <select id="selectFfsafeHostscanVulnResultByIds" parameterType="Long" resultMap="FfsafeHostscanVulnResultResult">
        <include refid="selectFfsafeHostscanVulnresultVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertFfsafeHostscanVulnResult" parameterType="FfsafeHostscanVulnResult" useGeneratedKeys="true" keyProperty="id">
        insert into ffsafe_hostscan_vulnresult
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">task_id,</if>
            <if test="hostIp != null">host_ip,</if>
            <if test="hostPort != null">host_port,</if>
            <if test="versionInfo != null">version_info,</if>
            <if test="vulnId != null">vuln_id,</if>
            <if test="vulnName != null">vuln_name,</if>
            <if test="riskLevel != null">risk_level,</if>
            <if test="cveNumber != null">cve_number,</if>
            <if test="cnnvdNumber != null">cnnvd_number,</if>
            <if test="vulnType != null">vuln_type,</if>
            <if test="publishDate != null">publish_date,</if>
            <if test="vulnInfo != null">vuln_info,</if>
            <if test="vulnSolve != null">vuln_solve,</if>
            <if test="evidence != null">evidence,</if>
            <if test="cpe != null">cpe,</if>
            <if test="summaryId != null">summary_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">#{taskId},</if>
            <if test="hostIp != null">#{hostIp},</if>
            <if test="hostPort != null">#{hostPort},</if>
            <if test="versionInfo != null">#{versionInfo},</if>
            <if test="vulnId != null">#{vulnId},</if>
            <if test="vulnName != null">#{vulnName},</if>
            <if test="riskLevel != null">#{riskLevel},</if>
            <if test="cveNumber != null">#{cveNumber},</if>
            <if test="cnnvdNumber != null">#{cnnvdNumber},</if>
            <if test="vulnType != null">#{vulnType},</if>
            <if test="publishDate != null">#{publishDate},</if>
            <if test="vulnInfo != null">#{vulnInfo},</if>
            <if test="vulnSolve != null">#{vulnSolve},</if>
            <if test="evidence != null">#{evidence},</if>
            <if test="cpe != null">#{cpe},</if>
            <if test="summaryId != null">#{summaryId},</if>
        </trim>
    </insert>

    <update id="updateFfsafeHostscanVulnResult" parameterType="FfsafeHostscanVulnresult">
        update ffsafe_hostscan_vulnresult
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="hostIp != null">host_ip = #{hostIp},</if>
            <if test="hostPort != null">host_port = #{hostPort},</if>
            <if test="versionInfo != null">version_info = #{versionInfo},</if>
            <if test="vulnId != null">vuln_id = #{vulnId},</if>
            <if test="vulnName != null">vuln_name = #{vulnName},</if>
            <if test="riskLevel != null">risk_level = #{riskLevel},</if>
            <if test="cveNumber != null">cve_number = #{cveNumber},</if>
            <if test="cnnvdNumber != null">cnnvd_number = #{cnnvdNumber},</if>
            <if test="vulnType != null">vuln_type = #{vulnType},</if>
            <if test="publishDate != null">publish_date = #{publishDate},</if>
            <if test="vulnInfo != null">vuln_info = #{vulnInfo},</if>
            <if test="vulnSolve != null">vuln_solve = #{vulnSolve},</if>
            <if test="evidence != null">evidence = #{evidence},</if>
            <if test="cpe != null">cpe = #{cpe},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFfsafeHostscanVulnResultById" parameterType="Long">
        delete from ffsafe_hostscan_vulnresult where id = #{id}
    </delete>

    <delete id="deleteFfsafeHostscanVulnResultByIds" parameterType="String">
        delete from ffsafe_hostscan_vulnresult where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getHostRiskLevelStat" parameterType="Long" resultType="java.util.HashMap">
        select max_risk_level, count(host_ip) as host_num from (
           select host_ip, max(risk_level) as max_risk_level
           from ffsafe_hostscan_vulnresult
           where task_id = #{taskid}
           group by host_ip
       ) t group by max_risk_level
           order by max_risk_level desc
    </select>

</mapper>