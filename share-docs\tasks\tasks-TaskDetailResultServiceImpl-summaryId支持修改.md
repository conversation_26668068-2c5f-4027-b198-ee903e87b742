# TaskDetailResultServiceImpl 添加 summaryId 支持修改文档

## 修改概述

本次修改为 `TaskDetailResultServiceImpl` 类添加了 `summaryId` 支持，实现了扫描任务详细结果与汇总记录的关联。

## 修改时间
- 开始时间：2025-01-22 14:30
- 完成时间：2025-01-22 15:00

## 修改范围

### 1. 数据库表结构修改

为以下 5 个表添加了 `summary_id` 字段：

| 表名 | 字段名 | 字段类型 | 是否允许NULL | 注释 |
|------|--------|----------|--------------|------|
| `ffsafe_hostscan_taskresult` | `summary_id` | `INT` | YES | 汇总记录ID |
| `ffsafe_hostscan_vulnresult` | `summary_id` | `INT` | YES | 汇总记录ID |
| `ffsafe_hostscan_portresult` | `summary_id` | `INT` | YES | 汇总记录ID |
| `ffsafe_hostscan_wpresult` | `summary_id` | `INT` | YES | 汇总记录ID |
| `ffsafe_webscan_vulnresult` | `summary_id` | `INT` | YES | 汇总记录ID |

**执行的 SQL 语句：**
```sql
-- 添加字段
ALTER TABLE ffsafe_hostscan_taskresult ADD COLUMN summary_id INT NULL COMMENT '汇总记录ID';
ALTER TABLE ffsafe_hostscan_vulnresult ADD COLUMN summary_id INT NULL COMMENT '汇总记录ID';
ALTER TABLE ffsafe_hostscan_portresult ADD COLUMN summary_id INT NULL COMMENT '汇总记录ID';
ALTER TABLE ffsafe_hostscan_wpresult ADD COLUMN summary_id INT NULL COMMENT '汇总记录ID';
ALTER TABLE ffsafe_webscan_vulnresult ADD COLUMN summary_id INT NULL COMMENT '汇总记录ID';

-- 修改字段类型为 INT（与 ffsafe_scantask_summary.id 保持一致）
ALTER TABLE ffsafe_hostscan_taskresult MODIFY COLUMN summary_id INT NULL COMMENT '汇总记录ID';
ALTER TABLE ffsafe_hostscan_vulnresult MODIFY COLUMN summary_id INT NULL COMMENT '汇总记录ID';
ALTER TABLE ffsafe_hostscan_portresult MODIFY COLUMN summary_id INT NULL COMMENT '汇总记录ID';
ALTER TABLE ffsafe_hostscan_wpresult MODIFY COLUMN summary_id INT NULL COMMENT '汇总记录ID';
ALTER TABLE ffsafe_webscan_vulnresult MODIFY COLUMN summary_id INT NULL COMMENT '汇总记录ID';
```

### 2. 实体类修改

为以下 5 个实体类添加了 `summaryId` 字段（Long 类型）：

#### 2.1 FfsafeHostscanTaskResult.java
```java
/** 汇总记录ID */
@Excel(name = "汇总记录ID")
private Long summaryId;
```

#### 2.2 FfsafeHostscanVulnResult.java
```java
/** 汇总记录ID */
@Excel(name = "汇总记录ID")
private Long summaryId;
```

#### 2.3 FfsafeHostscanPortResult.java
```java
/** 汇总记录ID */
@Excel(name = "汇总记录ID")
private Long summaryId;
```

#### 2.4 FfsafeHostscanWpresult.java
```java
/** 汇总记录ID */
@Excel(name = "汇总记录ID")
private Long summaryId;
```

#### 2.5 FfsafeWebscanVulnResult.java
```java
/** 汇总记录ID */
@Excel(name = "汇总记录ID")
private Long summaryId;
```

### 3. Mapper XML 文件修改

#### 3.1 resultMap 修改
为所有相关 Mapper XML 文件的 `resultMap` 添加了 `summary_id` 字段映射：
```xml
<result property="summaryId" column="summary_id" />
```

#### 3.2 selectVo SQL 修改
在所有 `selectVo` SQL 语句中添加了 `summary_id` 字段：
```xml
<!-- 示例：FfsafeHostscanTaskresultMapper.xml -->
<sql id="selectFfsafeHostscanTaskresultVo">
    select id, task_id, ip, system_name, system_version, high_risk_num, middle_risk_num, low_risk_num, poc_risk_num, pw_num, port_num, summary_id from ffsafe_hostscan_taskresult
</sql>
```

#### 3.3 insert 语句修改
在所有 `insert` 语句中添加了 `summary_id` 字段处理：
```xml
<!-- 字段列表 -->
<if test="summaryId != null">summary_id,</if>

<!-- 值列表 -->
<if test="summaryId != null">#{summaryId},</if>
```

#### 3.4 查询条件修改
为所有列表查询方法添加了 `summary_id` 查询条件：
```xml
<if test="summaryId != null "> and summary_id = #{summaryId}</if>
```

**修改的文件列表：**
- `FfsafeHostscanTaskresultMapper.xml`
- `FfsafeHostscanVulnresultMapper.xml`
- `FfsafeHostscanPortresultMapper.xml`
- `FfsafeHostscanWpresultMapper.xml`
- `FfsafeWebscanVulnResultMapper.xml`

### 4. Service 层修改

#### 4.1 TaskDetailResultServiceImpl.java 主要方法修改

**dealTaskDetailResult 方法修改：**
```java
@Override
public boolean dealTaskDetailResult(int taskId, HostScanTaskSummaryResult hostScanTaskSummaryResult, HostScanTaskDetailResult hostScanTaskDetailResult, SysJob sysJob) {
    boolean bRet = false;
    SqlSession sqlSession = null;
    
    // 查询汇总记录ID
    FfsafeScantaskSummary ffsafeScantaskSummary = new FfsafeScantaskSummary();
    ffsafeScantaskSummary.setTaskId(taskId);
    ffsafeScantaskSummary.setTaskType(2); // 主机漏扫任务
    ffsafeScantaskSummary.setJobId(sysJob.getJobId().intValue());
    List<FfsafeScantaskSummary> ffsafeScantaskSummaryList = ffsafeScantaskSummaryService.selectFfsafeScantaskSummaryList(ffsafeScantaskSummary);
    if ((ffsafeScantaskSummaryList == null) || (ffsafeScantaskSummaryList.size() == 0)) {
        return false;
    }
    
    Long summaryId = ffsafeScantaskSummaryList.get(0).getId();
    
    // 修改方法调用，传递 summaryId 参数
    List<FfsafeHostscanTaskResult> ffsafeHostscanTaskResultList = getFfsafeHostscanTaskList(taskId, hostScanTaskDetailResult, summaryId);
    List<FfsafeHostscanVulnResult> ffsafeHostscanVulnResultList = getFfsafeHostscanVulnList(taskId, hostScanTaskDetailResult, summaryId);
    List<FfsafeHostscanPortResult> ffsafeHostscanPortResultList = getFfsafeHostscanPortList(taskId, hostScanTaskDetailResult, summaryId);
    List<FfsafeHostscanWpresult> ffsafeHostscanWpresultList = getFfsafeHostscanWpresultList(taskId, hostScanTaskDetailResult, summaryId);
    
    // ... 其余代码保持不变
}
```

**dealWebscanTaskDetailResult 方法修改：**
```java
@Override
public boolean dealWebscanTaskDetailResult(int taskId, WebScanTaskSummaryResult webScanTaskSummaryResult, List<WebscanTaskDetailResult> webscanTaskDetailResultList, SysJob sysJob) {
    boolean bRet = false;
    SqlSession sqlSession = null;

    // 查询汇总记录ID
    FfsafeScantaskSummary ffsafeScantaskSummary = new FfsafeScantaskSummary();
    ffsafeScantaskSummary.setTaskId(taskId);
    ffsafeScantaskSummary.setTaskType(1); // web漏扫任务
    ffsafeScantaskSummary.setJobId(sysJob.getJobId().intValue());
    List<FfsafeScantaskSummary> ffsafeScantaskSummaryList = ffsafeScantaskSummaryService.selectFfsafeScantaskSummaryList(ffsafeScantaskSummary);
    if ((ffsafeScantaskSummaryList == null) || (ffsafeScantaskSummaryList.size() == 0)) {
        return false;
    }
    
    Long summaryId = ffsafeScantaskSummaryList.get(0).getId();

    // 修改方法调用，传递 summaryId 参数
    List<FfsafeWebscanVulnResult> ffsafeWebscanVulnResultList = getFfsafeWebscanVulnResultList(taskId, webscanTaskDetailResultList, summaryId);
    
    // ... 其余代码保持不变
}
```

#### 4.2 私有方法签名修改

修改了 5 个私有方法的签名，添加 `summaryId` 参数：

**1. getFfsafeHostscanTaskList 方法：**
```java
private List<FfsafeHostscanTaskResult> getFfsafeHostscanTaskList(int taskId, HostScanTaskDetailResult hostScanTaskDetailResult, Long summaryId) {
    List<FfsafeHostscanTaskResult> hostscanTaskResultList = new ArrayList<FfsafeHostscanTaskResult>();
    List<HostScanTaskDetailResult.Task> taskList = hostScanTaskDetailResult.getTaskList();
    for (HostScanTaskDetailResult.Task taskInfo: taskList) {
        FfsafeHostscanTaskResult ffsafeHostscanTaskResult = taskInfo.toHostScanTaskResult(taskId);
        ffsafeHostscanTaskResult.setSummaryId(summaryId); // 设置 summaryId
        hostscanTaskResultList.add(ffsafeHostscanTaskResult);
    }
    return hostscanTaskResultList;
}
```

**2. getFfsafeHostscanVulnList 方法：**
```java
private List<FfsafeHostscanVulnResult> getFfsafeHostscanVulnList(int taskId, HostScanTaskDetailResult hostScanTaskDetailResult, Long summaryId) {
    List<FfsafeHostscanVulnResult> hostscanVulnResultList = new ArrayList<FfsafeHostscanVulnResult>();
    List<HostScanTaskDetailResult.Expoloit> expoloitList = hostScanTaskDetailResult.getExpoloitList();
    for (HostScanTaskDetailResult.Expoloit expoloitInfo: expoloitList) {
        List<FfsafeHostscanVulnResult> tempVulnList = expoloitInfo.toHostScanTaskVulnList(taskId);
        for (FfsafeHostscanVulnResult vulnResult : tempVulnList) {
            vulnResult.setSummaryId(summaryId); // 设置 summaryId
        }
        hostscanVulnResultList.addAll(tempVulnList);
    }
    return hostscanVulnResultList;
}
```

**3. getFfsafeHostscanPortList 方法：**
```java
private List<FfsafeHostscanPortResult> getFfsafeHostscanPortList(int taskId, HostScanTaskDetailResult hostScanTaskDetailResult, Long summaryId) {
    List<FfsafeHostscanPortResult> hostscanPortResultList = new ArrayList<FfsafeHostscanPortResult>();
    List<HostScanTaskDetailResult.Port> portList = hostScanTaskDetailResult.getPortList();
    for (HostScanTaskDetailResult.Port portInfo: portList) {
        FfsafeHostscanPortResult ffsafeHostscanPortResult = portInfo.toHostScanTaskPort(taskId);
        ffsafeHostscanPortResult.setSummaryId(summaryId); // 设置 summaryId
        hostscanPortResultList.add(ffsafeHostscanPortResult);
    }
    return hostscanPortResultList;
}
```

**4. getFfsafeHostscanWpresultList 方法：**
```java
private List<FfsafeHostscanWpresult> getFfsafeHostscanWpresultList(int taskId, HostScanTaskDetailResult hostScanTaskDetailResult, Long summaryId) {
    List<FfsafeHostscanWpresult> ffsafeHostscanWpresultList = new ArrayList<FfsafeHostscanWpresult>();
    List<HostScanTaskDetailResult.WeakPassword> weakPasswordList = hostScanTaskDetailResult.getWeakPasswordList();
    for (HostScanTaskDetailResult.WeakPassword weakPasswordInfo: weakPasswordList) {
        FfsafeHostscanWpresult ffsafeHostscanWpresult = weakPasswordInfo.toHostScanTaskWp(taskId);
        ffsafeHostscanWpresult.setSummaryId(summaryId); // 设置 summaryId
        ffsafeHostscanWpresultList.add(ffsafeHostscanWpresult);
    }
    return ffsafeHostscanWpresultList;
}
```

**5. getFfsafeWebscanVulnResultList 方法：**
```java
private List<FfsafeWebscanVulnResult> getFfsafeWebscanVulnResultList(int taskId, List<WebscanTaskDetailResult> webscanTaskDetailResult, Long summaryId) {
    List<FfsafeWebscanVulnResult> ffsafeWebscanVulnResultList = new ArrayList<FfsafeWebscanVulnResult>();
    for (WebscanTaskDetailResult taskDetailResult : webscanTaskDetailResult) {
        List<FfsafeWebscanVulnResult> tempList = taskDetailResult.toFfsafeWebscanVulnList(taskId);
        for (FfsafeWebscanVulnResult vulnResult : tempList) {
            vulnResult.setSummaryId(summaryId); // 设置 summaryId
        }
        ffsafeWebscanVulnResultList.addAll(tempList);
    }
    return ffsafeWebscanVulnResultList;
}
```

## 技术要点

### 1. 数据类型一致性
- **数据库字段类型**：`INT`（与 `ffsafe_scantask_summary.id` 保持一致）
- **Java 实体类字段类型**：`Long`（保持原有设计）
- **类型转换处理**：`sysJob.getJobId().intValue()`（Long → Integer）

### 2. 任务类型区分
- **主机漏扫任务**：`taskType = 2`
- **Web漏扫任务**：`taskType = 1`

### 3. 空值处理
- 添加了汇总记录查询结果的空值检查
- 避免 `NullPointerException`

### 4. 批量数据处理
- 在处理 List 集合时，为每个元素正确设置了 `summaryId`
- 确保数据关联的完整性

## 验证结果

### 1. 编译验证
- Maven 编译成功，无任何错误
- 所有语法和类型检查通过

### 2. 数据库验证
- 所有表都正确添加了 `summary_id` 字段
- 字段类型与关联表保持一致

### 3. 代码一致性验证
- 所有修改保持了与现有代码的一致性
- 遵循了项目编码规范和命名约定

## 影响范围

### 1. 正面影响
- 实现了扫描任务详细结果与汇总记录的关联
- 支持通过 `summaryId` 进行数据查询和过滤
- 提高了数据的可追溯性和管理能力

### 2. 兼容性
- 新增字段允许 NULL，不影响现有数据
- 现有查询逻辑保持不变
- 向后兼容

## 后续建议

1. **数据迁移**：对于历史数据，可考虑编写脚本补充 `summary_id` 值
2. **索引优化**：根据查询频率，可考虑为 `summary_id` 字段添加索引
3. **外键约束**：可考虑添加外键约束确保数据完整性
4. **测试验证**：建议进行完整的功能测试，确保修改的正确性

## 修改完成确认

- ✅ 数据库表结构修改完成
- ✅ 实体类修改完成
- ✅ Mapper XML 文件修改完成
- ✅ Service 层代码修改完成
- ✅ 编译验证通过
- ✅ 查询条件添加完成

**修改人员**：Claude 4.0 sonnet  
**审核状态**：已完成  
**文档版本**：v1.0
