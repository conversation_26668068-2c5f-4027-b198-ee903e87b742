2025-08-22 15:06:24.044 [main] INFO  o.s.b.w.e.t.TomcatWebServer - [initialize,108] - Tomcat initialized with port(s): 8080 (http)
2025-08-22 15:06:24.103 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-22 15:06:24.111 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
2025-08-22 15:06:24.113 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.63]
2025-08-22 15:06:24.295 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
2025-08-22 15:06:24.301 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - [prepareWebApplicationContext,290] - Root WebApplicationContext: initialization completed in 9163 ms
2025-08-22 15:06:28.858 [main] INFO  o.s.b.w.s.RegistrationBean - [onStartup,50] - Filter hiddenHttpMethodFilter was not registered (disabled)
